# Руководство по использованию системы уведомлений

## Обзор

Система уведомлений позволяет администраторам отправлять уведомления пользователям системы EduNite. Поддерживаются различные типы уведомлений, таргетирование по группам пользователей, email-уведомления и отложенная отправка.

## Доступ к системе

1. Войдите в админ-панель как администратор
2. В боковом меню выберите **Management** → **Notifications**
3. Откроется страница с двумя вкладками:
   - **Send Notification** - создание и отправка уведомлений
   - **Sent Notifications** - просмотр отправленных уведомлений

## Создание уведомления

### Основная информация

1. **Заголовок** (обязательно) - краткое описание уведомления
2. **Тип уведомления**:
   - **Info** (ℹ️) - информационное сообщение
   - **Warning** (⚠️) - предупреждение
   - **Success** (✅) - уведомление об успехе
   - **Error** (❌) - сообщение об ошибке
   - **Announcement** (📢) - важное объявление

3. **Приоритет**:
   - **Low** - низкий приоритет
   - **Normal** - обычный приоритет
   - **High** - высокий приоритет
   - **Urgent** - срочное уведомление

4. **Сообщение** (обязательно) - полный текст уведомления

### Выбор получателей

Выберите одну из опций:

- **All Users** - отправить всем пользователям системы
- **By Role** - отправить пользователям с определенной ролью (Admin, Teacher, Student)
- **Specific User** - отправить конкретному пользователю
- **By Degree** - отправить студентам определенной специальности
- **By Course** - отправить студентам определенного курса
- **By Thread** - отправить студентам определенного потока

### Email-уведомления

1. Установите флажок **Send email notification** для отправки email
2. Укажите **Email Subject** - тему письма
3. Выберите **Email Template**:
   - **default_notification** - стандартный шаблон
   - **announcement** - шаблон для объявлений
   - **urgent_notification** - шаблон для срочных уведомлений

### Отложенная отправка

Для отправки уведомления в определенное время:
1. Выберите дату и время в поле **Schedule for later**
2. Оставьте поле пустым для немедленной отправки

## Просмотр отправленных уведомлений

На вкладке **Sent Notifications** вы можете:

- Просмотреть все отправленные уведомления
- Увидеть статус каждого уведомления (отправлено/запланировано)
- Проверить информацию о получателях
- Посмотреть время создания и отправки

## Типы уведомлений по цветам

- **Info** - синий цвет
- **Warning** - желтый цвет  
- **Success** - зеленый цвет
- **Error** - красный цвет
- **Announcement** - фиолетовый цвет

## Приоритеты по цветам

- **Low** - серый цвет
- **Normal** - синий цвет
- **High** - оранжевый цвет
- **Urgent** - красный цвет

## Примеры использования

### Объявление для всех пользователей
- **Тип**: Announcement
- **Приоритет**: High
- **Получатели**: All Users
- **Email**: Включить с темой "Важное объявление"

### Напоминание для студентов
- **Тип**: Info
- **Приоритет**: Normal
- **Получатели**: By Role → Student
- **Email**: По желанию

### Срочное уведомление для преподавателей
- **Тип**: Error
- **Приоритет**: Urgent
- **Получатели**: By Role → Teacher
- **Email**: Включить с шаблоном urgent_notification

### Уведомление для конкретного курса
- **Тип**: Info
- **Приоритет**: Normal
- **Получатели**: By Course → выбрать курс
- **Отложенная отправка**: За день до занятия

## Технические детали

### API Endpoints
- `POST /notifications` - создание уведомления (только админы)
- `POST /teacher/notifications` - создание уведомления (преподаватели)
- `GET /notifications` - получение всех уведомлений (админы)
- `GET /users/{id}/notifications` - получение уведомлений пользователя

### Поддерживаемые переменные в email-шаблонах
- `{{title}}` - заголовок уведомления
- `{{message}}` - текст сообщения

## Устранение неполадок

### Уведомление не отправляется
1. Проверьте обязательные поля (заголовок, сообщение)
2. Убедитесь, что выбран получатель (если не "All Users")
3. Проверьте подключение к серверу уведомлений

### Email не приходит
1. Проверьте настройки email-сервера
2. Убедитесь, что указана тема письма
3. Проверьте правильность email-адресов получателей

### Отложенное уведомление не отправилось
1. Убедитесь, что время указано в будущем
2. Проверьте работу планировщика задач
3. Проверьте логи системы

## Поддержка

При возникновении проблем обратитесь к системному администратору или проверьте логи в разделе **System** → **Logs**.
