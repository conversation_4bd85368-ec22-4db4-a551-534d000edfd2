СПЕЦИФИКАЦИЯ API ДЛЯ DASHBOARD И ANALYTICS
===========================================

Данная спецификация описывает необходимые эндпоинты API для замены моковых данных 
в страницах Dashboard и Analytics реальной статистикой.

1. DASHBOARD OVERVIEW - ОСНОВНЫЕ МЕТРИКИ
========================================

Эндпоинт: GET /api/analytics/overview

Формат ответа:
{
  "stats": {
    "totalUsers": {
      "value": 2318,
      "trend": "up",
      "trendValue": "+6.08%",
      "previousPeriodValue": 2185
    },
    "totalCourses": {
      "value": 156,
      "trend": "up", 
      "trendValue": "+15.03%",
      "previousPeriodValue": 135
    },
    "totalThreads": {
      "value": 3671,
      "trend": "down",
      "trendValue": "-0.03%",
      "previousPeriodValue": 3672
    },
    "activeStudents": {
      "value": 7265,
      "trend": "up",
      "trendValue": "+11.01%",
      "previousPeriodValue": 6540
    }
  },
  "period": "last_30_days"
}

2. ПОЛЬЗОВАТЕЛИ ПО ВРЕМЕНИ (ГРАФИК)
===================================

Эндпоинт: GET /api/analytics/users-timeline

Параметры: 
- period (optional): 7d, 30d, 90d, 1y (default: 30d)
- granularity (optional): day, week, month (default: day)

Формат ответа:
{
  "timeline": [
    {
      "date": "2024-01-01",
      "totalUsers": 10000,
      "newUsers": 150,
      "activeUsers": 8500
    },
    {
      "date": "2024-01-02", 
      "totalUsers": 10150,
      "newUsers": 200,
      "activeUsers": 8700
    }
  ],
  "comparison": {
    "previousPeriod": [
      {
        "date": "2023-12-01",
        "totalUsers": 5000,
        "newUsers": 100,
        "activeUsers": 4200
      }
    ]
  }
}

3. СТАТИСТИКА ПО УСТРОЙСТВАМ И ЛОКАЦИЯМ
=======================================

Эндпоинт: GET /api/analytics/user-demographics

Формат ответа:
{
  "devices": [
    { "name": "Windows", "value": 30, "percentage": 30.0 },
    { "name": "Mac", "value": 25, "percentage": 25.0 },
    { "name": "Linux", "value": 15, "percentage": 15.0 },
    { "name": "iOS", "value": 20, "percentage": 20.0 },
    { "name": "Android", "value": 15, "percentage": 15.0 },
    { "name": "Other", "value": 5, "percentage": 5.0 }
  ],
  "locations": [
    { "name": "Kazakhstan", "value": 52.1, "percentage": 52.1 },
    { "name": "Russia", "value": 22.8, "percentage": 22.8 },
    { "name": "USA", "value": 13.9, "percentage": 13.9 },
    { "name": "Other", "value": 11.2, "percentage": 11.2 }
  ]
}

4. ПОСЛЕДНИЕ АКТИВНОСТИ (ТАБЛИЦА)
=================================

Эндпоинт: GET /api/analytics/recent-activities

Параметры:
- limit (optional): количество записей (default: 10)
- type (optional): enrollment, assignment, login, all (default: all)

Формат ответа:
{
  "activities": [
    {
      "id": "ACT-001",
      "type": "enrollment",
      "user": {
        "id": 123,
        "name": "John Doe",
        "email": "<EMAIL>"
      },
      "course": {
        "id": 45,
        "title": "Introduction to Computer Science"
      },
      "date": "2024-01-15T10:30:00Z",
      "status": "completed"
    },
    {
      "id": "ACT-002", 
      "type": "assignment_submission",
      "user": {
        "id": 124,
        "name": "Jane Smith",
        "email": "<EMAIL>"
      },
      "assignment": {
        "id": 67,
        "title": "Final Project"
      },
      "date": "2024-01-15T09:45:00Z",
      "status": "pending"
    }
  ]
}

5. ANALYTICS - ДЕТАЛЬНАЯ АНАЛИТИКА
==================================

Эндпоинт: GET /api/analytics/detailed-stats

Параметры:
- period: 7d, 30d, 90d, 1y
- metrics: revenue, activity, performance (можно несколько через запятую)

Формат ответа:
{
  "courseEnrollments": {
    "timeline": [
      {
        "date": "2024-01-01",
        "enrollments": 12000,
        "completions": 8500
      }
    ],
    "comparison": {
      "previousPeriod": [
        {
          "date": "2023-01-01", 
          "enrollments": 10000,
          "completions": 7200
        }
      ]
    }
  },
  "userActivity": {
    "daily": [
      { "day": "Monday", "activeUsers": 420 },
      { "day": "Tuesday", "activeUsers": 380 },
      { "day": "Wednesday", "activeUsers": 450 },
      { "day": "Thursday", "activeUsers": 520 },
      { "day": "Friday", "activeUsers": 490 },
      { "day": "Saturday", "activeUsers": 380 },
      { "day": "Sunday", "activeUsers": 320 }
    ]
  },
  "trafficSources": [
    { "source": "Direct", "value": 35, "percentage": 35.0 },
    { "source": "Organic Search", "value": 25, "percentage": 25.0 },
    { "source": "Referral", "value": 15, "percentage": 15.0 },
    { "source": "Social Media", "value": 15, "percentage": 15.0 },
    { "source": "Email", "value": 8, "percentage": 8.0 },
    { "source": "Other", "value": 2, "percentage": 2.0 }
  ]
}

6. КЛЮЧЕВЫЕ МЕТРИКИ ПРОИЗВОДИТЕЛЬНОСТИ
======================================

Эндпоинт: GET /api/analytics/performance-metrics

Формат ответа:
{
  "metrics": {
    "courseCompletionRate": {
      "value": 3.2,
      "unit": "%",
      "trend": "up",
      "change": "+0.5%",
      "description": "Course completion rate"
    },
    "averageGrade": {
      "value": 85.20,
      "unit": "points",
      "trend": "up", 
      "change": "+3.50",
      "description": "Average assignment grade"
    },
    "attendanceRate": {
      "value": 42.8,
      "unit": "%",
      "trend": "down",
      "change": "-2.1%", 
      "description": "Average attendance rate"
    },
    "averageSessionDuration": {
      "value": "2m 45s",
      "unit": "time",
      "trend": "up",
      "change": "+15s",
      "description": "Average session duration"
    }
  }
}

7. ДОПОЛНИТЕЛЬНЫЕ ЭНДПОИНТЫ
===========================

Курсы:
- GET /api/analytics/courses/popular - самые популярные курсы
- GET /api/analytics/courses/completion-rates - статистика завершения курсов

Студенты:
- GET /api/analytics/students/engagement - вовлеченность студентов
- GET /api/analytics/students/performance - успеваемость студентов

Преподаватели:
- GET /api/analytics/teachers/activity - активность преподавателей
- GET /api/analytics/teachers/course-load - нагрузка преподавателей

Посещаемость:
- GET /api/analytics/attendance/trends - тренды посещаемости
- GET /api/analytics/attendance/by-course - посещаемость по курсам

8. ОБЩИЕ ПАРАМЕТРЫ ФИЛЬТРАЦИИ
=============================

Все эндпоинты должны поддерживать следующие параметры:
- start_date и end_date - диапазон дат
- semester_id - фильтр по семестру
- course_id - фильтр по курсу
- degree_id - фильтр по программе обучения

9. ФОРМАТ ОШИБОК
================

{
  "error": {
    "code": "ANALYTICS_ERROR",
    "message": "Unable to fetch analytics data",
    "details": "Database connection failed"
  }
}

ПРИМЕЧАНИЯ
==========

1. Все даты должны быть в формате ISO 8601 (YYYY-MM-DDTHH:mm:ssZ)
2. Тренды могут быть: "up", "down", "neutral"
3. Процентные значения должны быть числами с плавающей точкой
4. Все эндпоинты должны поддерживать CORS для фронтенда
5. Требуется авторизация через Bearer token для всех эндпоинтов
