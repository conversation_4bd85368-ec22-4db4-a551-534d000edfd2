import React, { useState, useEffect } from "react";
import { FiUsers, FiShoppingBag, FiDollarSign, FiActivity, FiEye, FiArrowUp, FiArrowRight, FiArrowDown } from "react-icons/fi";
import Card from "../components/Card";
import StatCard from "../components/StatCard";
import Table from "../components/Table";
import Chart from "../components/Chart";
import { analyticsService } from "../services/api";

const Dashboard = () => {
   const [loading, setLoading] = useState(true);
   const [overviewData, setOverviewData] = useState(null);
   const [usersTimelineData, setUsersTimelineData] = useState(null);
   const [demographicsData, setDemographicsData] = useState(null);
   const [recentActivitiesData, setRecentActivitiesData] = useState(null);
   const [error, setError] = useState(null);

   useEffect(() => {
      const fetchDashboardData = async () => {
         try {
            setLoading(true);
            setError(null);

            const [overview, usersTimeline, demographics, recentActivities] = await Promise.all([
               analyticsService.getOverview(),
               analyticsService.getUsersTimeline(),
               analyticsService.getUserDemographics(),
               analyticsService.getRecentActivities(5),
            ]);

            setOverviewData(overview);
            setUsersTimelineData(usersTimeline);
            setDemographicsData(demographics);
            setRecentActivitiesData(recentActivities);
         } catch (err) {
            console.error("Error fetching dashboard data:", err);
            setError("Failed to load dashboard data");
         } finally {
            setLoading(false);
         }
      };

      fetchDashboardData();
   }, []);

   // Transform overview data to stats format
   const getStatsFromOverview = () => {
      if (!overviewData) return [];

      const getTrendIcon = (trend) => {
         switch (trend) {
            case "up":
               return <FiArrowUp size={14} />;
            case "down":
               return <FiArrowDown size={14} />;
            default:
               return <FiArrowRight size={14} />;
         }
      };

      return [
         {
            title: "Active Students",
            value: overviewData.stats.activeStudents.value.toLocaleString(),
            trendIcon: getTrendIcon(overviewData.stats.activeStudents.trend),
            trend: overviewData.stats.activeStudents.trend,
            trendValue: overviewData.stats.activeStudents.trendValue,
         },
         {
            title: "Total Threads",
            value: overviewData.stats.totalThreads.value.toLocaleString(),
            trendIcon: getTrendIcon(overviewData.stats.totalThreads.trend),
            trend: overviewData.stats.totalThreads.trend,
            trendValue: overviewData.stats.totalThreads.trendValue,
         },
         {
            title: "Total Courses",
            value: overviewData.stats.totalCourses.value.toLocaleString(),
            trendIcon: getTrendIcon(overviewData.stats.totalCourses.trend),
            trend: overviewData.stats.totalCourses.trend,
            trendValue: overviewData.stats.totalCourses.trendValue,
         },
         {
            title: "Total Users",
            value: overviewData.stats.totalUsers.value.toLocaleString(),
            trendIcon: getTrendIcon(overviewData.stats.totalUsers.trend),
            trend: overviewData.stats.totalUsers.trend,
            trendValue: overviewData.stats.totalUsers.trendValue,
         },
      ];
   };

   const stats = getStatsFromOverview();

   // Transform users timeline data for chart
   const getUsersChartData = () => {
      if (!usersTimelineData) {
         return {
            labels: [],
            datasets: [],
         };
      }

      const currentLabels = usersTimelineData.timeline.map((item) =>
         new Date(item.date).toLocaleDateString("en-US", { month: "short", day: "numeric" })
      );
      const currentData = usersTimelineData.timeline.map((item) => item.totalUsers);

      const previousLabels = usersTimelineData.comparison.previousPeriod.map((item) =>
         new Date(item.date).toLocaleDateString("en-US", { month: "short", day: "numeric" })
      );
      const previousData = usersTimelineData.comparison.previousPeriod.map((item) => item.totalUsers);

      return {
         labels: currentLabels,
         datasets: [
            {
               label: "This period",
               data: currentData,
               borderColor: "rgb(79, 70, 229)", // Indigo
               backgroundColor: "rgba(79, 70, 229, 0.1)",
               tension: 0.4,
               borderWidth: 2,
            },
            {
               label: "Previous period",
               data: previousData,
               borderColor: "rgb(209, 213, 219)", // Gray
               backgroundColor: "rgba(209, 213, 219, 0.1)",
               tension: 0.4,
               borderWidth: 2,
               borderDash: [5, 5],
            },
         ],
      };
   };

   const usersChartData = getUsersChartData();

   // Transform demographics data for charts
   const getDeviceChartData = () => {
      if (!demographicsData) {
         return {
            labels: [],
            datasets: [],
         };
      }

      return {
         labels: demographicsData.devices.map((device) => device.name),
         datasets: [
            {
               data: demographicsData.devices.map((device) => device.value),
               backgroundColor: [
                  "rgba(139, 92, 246, 0.7)", // Purple
                  "rgba(16, 185, 129, 0.7)", // Green
                  "rgba(0, 0, 0, 0.7)", // Black
                  "rgba(59, 130, 246, 0.7)", // Blue
                  "rgba(209, 213, 219, 0.7)", // Gray
                  "rgba(255, 159, 64, 0.7)", // Orange
               ],
               borderWidth: 0,
            },
         ],
      };
   };

   const getLocationChartData = () => {
      if (!demographicsData) {
         return {
            labels: [],
            datasets: [],
         };
      }

      return {
         labels: demographicsData.locations.map((location) => location.name),
         datasets: [
            {
               data: demographicsData.locations.map((location) => location.value),
               backgroundColor: [
                  "rgba(0, 0, 0, 0.7)", // Black
                  "rgba(209, 213, 219, 0.7)", // Gray
                  "rgba(59, 130, 246, 0.7)", // Blue
                  "rgba(139, 92, 246, 0.7)", // Purple
               ],
               borderWidth: 0,
            },
         ],
      };
   };

   const deviceChartData = getDeviceChartData();
   const locationChartData = getLocationChartData();

   const lineChartOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
         legend: {
            display: true,
            position: "top",
            align: "end",
            labels: {
               boxWidth: 8,
               usePointStyle: true,
               pointStyle: "circle",
            },
         },
         tooltip: {
            mode: "index",
            intersect: false,
         },
      },
      scales: {
         x: {
            grid: {
               display: false,
            },
         },
         y: {
            grid: {
               borderDash: [2, 2],
            },
            ticks: {
               callback: function (value) {
                  return value / 1000 + "k";
               },
            },
         },
      },
      elements: {
         point: {
            radius: 0,
            hoverRadius: 6,
         },
      },
   };

   const barChartOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
         legend: {
            display: false,
         },
         tooltip: {
            mode: "index",
            intersect: false,
         },
      },
      scales: {
         x: {
            grid: {
               display: false,
            },
         },
         y: {
            grid: {
               borderDash: [2, 2],
            },
            ticks: {
               callback: function (value) {
                  return value / 1000 + "k";
               },
            },
         },
      },
   };

   const pieChartOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
         legend: {
            position: "right",
            align: "center",
            labels: {
               boxWidth: 8,
               usePointStyle: true,
               pointStyle: "circle",
            },
         },
      },
      cutout: "65%",
   };

   // Sample data for table
   const columns = [
      { header: "Order ID", accessor: "id" },
      { header: "Customer", accessor: "customer" },
      { header: "Date", accessor: "date" },
      { header: "Amount", accessor: "amount" },
      {
         header: "Status",
         accessor: "status",
         render: (row) => {
            const statusColors = {
               completed: "bg-green-100 text-green-800",
               pending: "bg-yellow-100 text-yellow-800",
               cancelled: "bg-red-100 text-red-800",
            };

            return (
               <span className={`px-2 py-1 rounded-full text-xs ${statusColors[row.status.toLowerCase()]}`}>{row.status}</span>
            );
         },
      },
   ];

   const tableData = [
      { id: "#ORD-001", customer: "John Doe", date: "2023-04-24", amount: "$120.00", status: "Completed" },
      { id: "#ORD-002", customer: "Jane Smith", date: "2023-04-23", amount: "$85.50", status: "Pending" },
      { id: "#ORD-003", customer: "Robert Johnson", date: "2023-04-22", amount: "$210.25", status: "Completed" },
      { id: "#ORD-004", customer: "Emily Davis", date: "2023-04-21", amount: "$45.00", status: "Cancelled" },
      { id: "#ORD-005", customer: "Michael Brown", date: "2023-04-20", amount: "$175.75", status: "Completed" },
   ];

   const usersTabs = [
      { label: "Total Users", active: true },
      { label: "Total Projects", active: false },
      { label: "Operating Status", active: false },
   ];

   const websiteTabs = [
      { label: "Google", active: true },
      { label: "YouTube", active: false },
      { label: "Instagram", active: false },
      { label: "Pinterest", active: false },
      { label: "Facebook", active: false },
      { label: "Twitter", active: false },
   ];

   if (loading) {
      return (
         <div className="flex items-center justify-center h-64">
            <div className="text-center">
               <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
               <p className="text-gray-500">Loading dashboard data...</p>
            </div>
         </div>
      );
   }

   if (error) {
      return (
         <div className="flex items-center justify-center h-64">
            <div className="text-center">
               <p className="text-red-500 mb-4">{error}</p>
               <button
                  onClick={() => window.location.reload()}
                  className="px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600"
               >
                  Retry
               </button>
            </div>
         </div>
      );
   }

   return (
      <div>
         <h1 className="text-2xl font-bold mb-6">Overview</h1>

         {/* Stats Row */}
         <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            {stats.map((stat, index) => (
               <StatCard
                  key={index}
                  title={stat.title}
                  value={stat.value}
                  trendIcon={stat.trendIcon}
                  trend={stat.trend}
                  trendValue={stat.trendValue}
               />
            ))}
         </div>

         {/* Users Chart */}
         <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 mb-6">
            <div className="lg:col-span-2">
               <Card title="Total Users" tabs={usersTabs} className="h-full">
                  <div className="h-64">
                     <Chart type="line" data={usersChartData} options={lineChartOptions} />
                  </div>
               </Card>
            </div>

            <div>
               <Card title="Traffic by Website" className="h-full">
                  <div className="space-y-4">
                     {websiteTabs.map((tab, index) => (
                        <div key={index} className="flex items-center justify-between">
                           <span className="text-sm text-gray-700">{tab.label}</span>
                           <div className="flex items-center space-x-2">
                              <div className="w-24 h-1 bg-gray-200 rounded-full overflow-hidden">
                                 <div
                                    className="h-full bg-primary-500 rounded-full"
                                    style={{
                                       width:
                                          index === 0
                                             ? "70%"
                                             : index === 1
                                             ? "60%"
                                             : index === 2
                                             ? "50%"
                                             : index === 3
                                             ? "40%"
                                             : index === 4
                                             ? "30%"
                                             : "20%",
                                    }}
                                 ></div>
                              </div>
                              <span className="text-xs text-gray-500">
                                 {index === 0
                                    ? "52.1%"
                                    : index === 1
                                    ? "22.8%"
                                    : index === 2
                                    ? "13.9%"
                                    : index === 3
                                    ? "11.2%"
                                    : index === 4
                                    ? "6.5%"
                                    : "3.4%"}
                              </span>
                           </div>
                        </div>
                     ))}
                  </div>
               </Card>
            </div>
         </div>

         {/* Traffic Charts */}
         <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-6">
            <Card title="Traffic by Device">
               <div className="h-64">
                  <Chart type="bar" data={deviceChartData} options={barChartOptions} />
               </div>
            </Card>

            <Card title="Traffic by Location">
               <div className="h-64 flex items-center justify-center">
                  <div className="w-48 h-48">
                     <Chart type="doughnut" data={locationChartData} options={pieChartOptions} />
                  </div>
                  <div className="ml-4 space-y-2">
                     {locationChartData.labels.map((label, index) => (
                        <div key={index} className="flex items-center justify-between">
                           <div className="flex items-center">
                              <span
                                 className="inline-block w-2 h-2 rounded-full mr-2"
                                 style={{ backgroundColor: locationChartData.datasets[0].backgroundColor[index] }}
                              ></span>
                              <span className="text-sm text-gray-700">{label}</span>
                           </div>
                           <span className="text-xs text-gray-500 ml-4">{locationChartData.datasets[0].data[index]}%</span>
                        </div>
                     ))}
                  </div>
               </div>
            </Card>
         </div>

         {/* Recent Activities */}
         <Card title="Recent Activities" className="mb-6">
            {recentActivitiesData && recentActivitiesData.activities.length > 0 ? (
               <div className="space-y-4">
                  {recentActivitiesData.activities.map((activity, index) => (
                     <div key={activity.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center space-x-3">
                           <div
                              className={`w-2 h-2 rounded-full ${
                                 activity.type === "enrollment"
                                    ? "bg-green-500"
                                    : activity.type === "assignment_submission"
                                    ? "bg-blue-500"
                                    : activity.type === "login"
                                    ? "bg-gray-500"
                                    : "bg-purple-500"
                              }`}
                           ></div>
                           <div>
                              <p className="text-sm font-medium text-gray-900">{activity.user.name}</p>
                              <p className="text-xs text-gray-500">
                                 {activity.type === "enrollment" && activity.course
                                    ? `Enrolled in ${activity.course.title}`
                                    : activity.type === "assignment_submission" && activity.assignment
                                    ? `Submitted ${activity.assignment.title}`
                                    : activity.type === "login"
                                    ? "Logged in"
                                    : "Activity"}
                              </p>
                           </div>
                        </div>
                        <div className="text-right">
                           <p className="text-xs text-gray-500">{new Date(activity.date).toLocaleDateString()}</p>
                           <span
                              className={`inline-block px-2 py-1 rounded-full text-xs ${
                                 activity.status === "completed"
                                    ? "bg-green-100 text-green-800"
                                    : activity.status === "pending"
                                    ? "bg-yellow-100 text-yellow-800"
                                    : activity.status === "graded"
                                    ? "bg-blue-100 text-blue-800"
                                    : "bg-gray-100 text-gray-800"
                              }`}
                           >
                              {activity.status}
                           </span>
                        </div>
                     </div>
                  ))}
               </div>
            ) : (
               <div className="h-16 flex items-center justify-center text-gray-400">No recent activities</div>
            )}
         </Card>
      </div>
   );
};

export default Dashboard;
